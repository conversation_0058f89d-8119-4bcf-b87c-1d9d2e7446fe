﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{9DC9C405-08DF-470C-9E9A-E33187D3973D}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RLC</RootNamespace>
    <AssemblyName>RLC Toolkit-V3.0.5-Admin</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ApplicationIcon>TreeListMainDemo.ico</ApplicationIcon>
    <TargetZone>LocalIntranet</TargetZone>
    <GenerateManifests>true</GenerateManifests>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <SignManifests>false</SignManifests>
    <ManifestCertificateThumbprint>BE158745A85FB628C82581A336F40A71B053D4EC</ManifestCertificateThumbprint>
    <ManifestKeyFile>RLC_TemporaryKey.pfx</ManifestKeyFile>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.BonusSkins.v9.2, Version=*******, Culture=neutral, PublicKeyToken=95fc6c5621878f0a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.BonusSkins.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.Data.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.ExpressApp.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.ExpressApp.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.ExpressApp.Win.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.ExpressApp.Win.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.OfficeSkins.v9.2, Version=*******, Culture=neutral, PublicKeyToken=95fc6c5621878f0a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.OfficeSkins.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.Utils.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpo.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.Xpo.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.XtraBars.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.XtraEditors.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.XtraGrid.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.XtraLayout.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraNavBar.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.XtraNavBar.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraRichEdit.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.XtraRichEdit.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.XtraTreeList.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraVerticalGrid.v9.2, Version=*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\DevExpress.XtraVerticalGrid.v9.2.dll</HintPath>
    </Reference>
    <Reference Include="Ionic.Zip, Version=*******, Culture=neutral, PublicKeyToken=edbe51ad942a3f5c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Thu vien\Ionic.Zip.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Design" />
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\Common_Modules\AC_Out_Cfg.cs">
      <Link>Form\AC_Out_Cfg.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\AC_Out_Cfg.designer.cs">
      <Link>Form\AC_Out_Cfg.designer.cs</Link>
      <DependentUpon>AC_Out_Cfg.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Add Group.cs">
      <Link>Form\Add Group.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Add Group.designer.cs">
      <Link>Form\Add Group.designer.cs</Link>
      <DependentUpon>Add Group.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Add_Unit.cs">
      <Link>Form\Add_Unit.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Add_Unit.designer.cs">
      <Link>Form\Add_Unit.designer.cs</Link>
      <DependentUpon>Add_Unit.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Backup.cs">
      <Link>Form\Backup.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Backup.designer.cs">
      <Link>Form\Backup.designer.cs</Link>
      <DependentUpon>Backup.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\Bedside 12 ports\Input_Bedside_12T.cs">
      <Link>Form\Board\Bedside 12 ports\Input_Bedside_12T.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\Bedside 12 ports\Input_Bedside_12T.designer.cs">
      <Link>Form\Board\Bedside 12 ports\Input_Bedside_12T.designer.cs</Link>
      <DependentUpon>Input_Bedside_12T.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\Bedside 12 ports\IO_Bedside_12T.cs">
      <Link>Form\Board\Bedside 12 ports\IO_Bedside_12T.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\Bedside 12 ports\IO_Bedside_12T.designer.cs">
      <Link>Form\Board\Bedside 12 ports\IO_Bedside_12T.designer.cs</Link>
      <DependentUpon>IO_Bedside_12T.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\Bedside\ConfigIO.cs">
      <Link>Form\Board\Bedside\ConfigIO.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\Bedside\ConfigIO.designer.cs">
      <Link>Form\Board\Bedside\ConfigIO.designer.cs</Link>
      <DependentUpon>ConfigIO.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\Bedside\I_O_Bedside.cs">
      <Link>Form\Board\Bedside\I_O_Bedside.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\Bedside\I_O_Bedside.designer.cs">
      <Link>Form\Board\Bedside\I_O_Bedside.designer.cs</Link>
      <DependentUpon>I_O_Bedside.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\Board_Attribute.cs">
      <Link>Form\Board\Board_Attribute.cs</Link>
    </Compile>
    <Compile Include="..\Common_Modules\Board\BSP_R14_OL\ConfigIO_BSP_R14_OL.cs">
      <Link>Form\Board\BSP_R14_OL\ConfigIO_BSP_R14_OL.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\BSP_R14_OL\ConfigIO_BSP_R14_OL.designer.cs">
      <Link>Form\Board\BSP_R14_OL\ConfigIO_BSP_R14_OL.designer.cs</Link>
      <DependentUpon>ConfigIO_BSP_R14_OL.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-16RL-16AO\Input_RCU-16RL-16AO.cs">
      <Link>Form\Board\RCU-16RL-16AO\Input_RCU-16RL-16AO.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-16RL-16AO\Input_RCU-16RL-16AO.designer.cs">
      <Link>Form\Board\RCU-16RL-16AO\Input_RCU-16RL-16AO.designer.cs</Link>
      <DependentUpon>Input_RCU-16RL-16AO.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-16RL-16AO\IO_RCU-16RL-16AO.cs">
      <Link>Form\Board\RCU-16RL-16AO\IO_RCU-16RL-16AO.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-16RL-16AO\IO_RCU-16RL-16AO.designer.cs">
      <Link>Form\Board\RCU-16RL-16AO\IO_RCU-16RL-16AO.designer.cs</Link>
      <DependentUpon>IO_RCU-16RL-16AO.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-24RL-8AO\Input_RCU-24RL-8AO.cs">
      <Link>Form\Board\RCU-24RL-8AO\Input_RCU-24RL-8AO.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-24RL-8AO\Input_RCU-24RL-8AO.designer.cs">
      <Link>Form\Board\RCU-24RL-8AO\Input_RCU-24RL-8AO.designer.cs</Link>
      <DependentUpon>Input_RCU-24RL-8AO.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-24RL-8AO\IO_RCU-24RL-8AO.cs">
      <Link>Form\Board\RCU-24RL-8AO\IO_RCU-24RL-8AO.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-24RL-8AO\IO_RCU-24RL-8AO.designer.cs">
      <Link>Form\Board\RCU-24RL-8AO\IO_RCU-24RL-8AO.designer.cs</Link>
      <DependentUpon>IO_RCU-24RL-8AO.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-32AO\Input_RCU-32AO.cs">
      <Link>Form\Board\RCU-32AO\Input_RCU-32AO.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-32AO\Input_RCU-32AO.designer.cs">
      <Link>Form\Board\RCU-32AO\Input_RCU-32AO.designer.cs</Link>
      <DependentUpon>Input_RCU-32AO.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-32AO\IO_RCU-32AO.cs">
      <Link>Form\Board\RCU-32AO\IO_RCU-32AO.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-32AO\IO_RCU-32AO.designer.cs">
      <Link>Form\Board\RCU-32AO\IO_RCU-32AO.designer.cs</Link>
      <DependentUpon>IO_RCU-32AO.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-8RL-24AO\Input_RCU-8RL-24AO.cs">
      <Link>Form\Board\RCU-8RL-24AO\Input_RCU-8RL-24AO.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-8RL-24AO\Input_RCU-8RL-24AO.designer.cs">
      <Link>Form\Board\RCU-8RL-24AO\Input_RCU-8RL-24AO.designer.cs</Link>
      <DependentUpon>Input_RCU-8RL-24AO.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-8RL-24AO\IO_RCU-8RL-24AO.cs">
      <Link>Form\Board\RCU-8RL-24AO\IO_RCU-8RL-24AO.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RCU-8RL-24AO\IO_RCU-8RL-24AO.designer.cs">
      <Link>Form\Board\RCU-8RL-24AO\IO_RCU-8RL-24AO.designer.cs</Link>
      <DependentUpon>IO_RCU-8RL-24AO.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_new 16 ports\Input_RLC_new.cs">
      <Link>Form\Board\RLC_new 16 ports\Input_RLC_new.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_new 16 ports\Input_RLC_new.designer.cs">
      <Link>Form\Board\RLC_new 16 ports\Input_RLC_new.designer.cs</Link>
      <DependentUpon>Input_RLC_new.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_new 16 ports\IO_RLC_new.cs">
      <Link>Form\Board\RLC_new 16 ports\IO_RLC_new.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_new 16 ports\IO_RLC_new.designer.cs">
      <Link>Form\Board\RLC_new 16 ports\IO_RLC_new.designer.cs</Link>
      <DependentUpon>IO_RLC_new.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_new 20 ports\Input_RLC_new_20_ports.cs">
      <Link>Form\Board\RLC_new 20 ports\Input_RLC_new_20_ports.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_new 20 ports\Input_RLC_new_20_ports.designer.cs">
      <Link>Form\Board\RLC_new 20 ports\Input_RLC_new_20_ports.designer.cs</Link>
      <DependentUpon>Input_RLC_new_20_ports.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_new 20 ports\IO_RLC_new_20_ports.cs">
      <Link>Form\Board\RLC_new 20 ports\IO_RLC_new_20_ports.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_new 20 ports\IO_RLC_new_20_ports.designer.cs">
      <Link>Form\Board\RLC_new 20 ports\IO_RLC_new_20_ports.designer.cs</Link>
      <DependentUpon>IO_RLC_new_20_ports.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_old\ConfigIO_RLC.cs">
      <Link>Form\Board\RLC_old\ConfigIO_RLC.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_old\ConfigIO_RLC.designer.cs">
      <Link>Form\Board\RLC_old\ConfigIO_RLC.designer.cs</Link>
      <DependentUpon>ConfigIO_RLC.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_old\Input_RLC.cs">
      <Link>Form\Board\RLC_old\Input_RLC.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Board\RLC_old\Input_RLC.designer.cs">
      <Link>Form\Board\RLC_old\Input_RLC.designer.cs</Link>
      <DependentUpon>Input_RLC.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\Command\Command.cs">
      <Link>Form\Command\Command.cs</Link>
    </Compile>
    <Compile Include="..\Common_Modules\Light_Out_Cfg.cs">
      <Link>Form\Light_Out_Cfg.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\Light_Out_Cfg.designer.cs">
      <Link>Form\Light_Out_Cfg.designer.cs</Link>
      <DependentUpon>Light_Out_Cfg.cs</DependentUpon>
    </Compile>
    <Compile Include="..\Common_Modules\RS485_Config.cs">
      <Link>Form\RS485_Config.cs</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\Common_Modules\RS485_Config.designer.cs">
      <Link>Form\RS485_Config.designer.cs</Link>
      <DependentUpon>RS485_Config.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\BSP_R14_OL\Input_BSP_R14_OL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\BSP_R14_OL\Input_BSP_R14_OL.designer.cs">
      <DependentUpon>Input_BSP_R14_OL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-ETH2SKDL\Input_GNT_ETH2SKDL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-ETH2SKDL\Input_GNT_ETH2SKDL.designer.cs">
      <DependentUpon>Input_GNT_ETH2SKDL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-10AO\Input_GNT-EXT-10AO.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-10AO\Input_GNT-EXT-10AO.designer.cs">
      <DependentUpon>Input_GNT-EXT-10AO.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-12RL-12AO\Input_GNT-EXT-12RL-12AO.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-12RL-12AO\Input_GNT-EXT-12RL-12AO.designer.cs">
      <DependentUpon>Input_GNT-EXT-12RL-12AO.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-12RL\Input_GNT-EXT-12RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-12RL\Input_GNT-EXT-12RL.designer.cs">
      <DependentUpon>Input_GNT-EXT-12RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-20RL\Input_GNT-EXT-20RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-20RL\Input_GNT-EXT-20RL.designer.cs">
      <DependentUpon>Input_GNT-EXT-20RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-24IN\Input_GNT-EXT-24IN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-24IN\Input_GNT-EXT-24IN.designer.cs">
      <DependentUpon>Input_GNT-EXT-24IN.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-24IN\IO_GNT-EXT-24IN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-24IN\IO_GNT-EXT-24IN.designer.cs">
      <DependentUpon>IO_GNT-EXT-24IN.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-28AO\Input_GNT-EXT-28AO.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-28AO\Input_GNT-EXT-28AO.designer.cs">
      <DependentUpon>Input_GNT-EXT-28AO.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-48IN-T\Input_GNT-EXT-48IN-T.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-48IN-T\Input_GNT-EXT-48IN-T.designer.cs">
      <DependentUpon>Input_GNT-EXT-48IN-T.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-48IN-T\IO_GNT-EXT-48IN-T.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-48IN-T\IO_GNT-EXT-48IN-T.designer.cs">
      <DependentUpon>IO_GNT-EXT-48IN-T.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-8RL\Input_GNT-EXT-8RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-8RL\Input_GNT-EXT-8RL.designer.cs">
      <DependentUpon>Input_GNT-EXT-8RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-6RL\Input_GNT-EXT-6RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\GNT-EXT-6RL\Input_GNT-EXT-6RL.designer.cs">
      <DependentUpon>Input_GNT-EXT-6RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-11IN-4RL\Input_RCU_11IN_4RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-11IN-4RL\Input_RCU_11IN_4RL.designer.cs">
      <DependentUpon>Input_RCU_11IN_4RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-11IN-4RL\IO_RCU_11IN_4RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-11IN-4RL\IO_RCU_11IN_4RL.designer.cs">
      <DependentUpon>IO_RCU_11IN_4RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-10RL-T\Input_RCU_21IN_10RL_T.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-10RL-T\Input_RCU_21IN_10RL_T.designer.cs">
      <DependentUpon>Input_RCU_21IN_10RL_T.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-10RL-T\IO_RCU_21IN_10RL_T.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-10RL-T\IO_RCU_21IN_10RL_T.designer.cs">
      <DependentUpon>IO_RCU_21IN_10RL_T.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-10RL\Input_RCU_21IN_10RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-10RL\Input_RCU_21IN_10RL.designer.cs">
      <DependentUpon>Input_RCU_21IN_10RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-10RL\IO_RCU_21IN_10RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-10RL\IO_RCU_21IN_10RL.designer.cs">
      <DependentUpon>IO_RCU_21IN_10RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-8RL-K\Input_RCU_21IN_8RL_K.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-8RL-K\Input_RCU_21IN_8RL_K.designer.cs">
      <DependentUpon>Input_RCU_21IN_8RL_K.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-8RL-K\IO_RCU_21IN_8RL_K.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-8RL-K\IO_RCU_21IN_8RL_K.designer.cs">
      <DependentUpon>IO_RCU_21IN_8RL_K.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-8RL\Input_RCU_21IN_8RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-8RL\Input_RCU_21IN_8RL.designer.cs">
      <DependentUpon>Input_RCU_21IN_8RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-8RL\IO_RCU_21IN_8RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-21IN-8RL\IO_RCU_21IN_8RL.designer.cs">
      <DependentUpon>IO_RCU_21IN_8RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-30IN-10RL\Input_RCU_30IN_10RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-30IN-10RL\Input_RCU_30IN_10RL.designer.cs">
      <DependentUpon>Input_RCU_30IN_10RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-30IN-10RL\IO_RCU_30IN_10RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-30IN-10RL\IO_RCU_30IN_10RL.designer.cs">
      <DependentUpon>IO_RCU_30IN_10RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-48IN-16RL-4AO\Input_RCU_48IN_16RL_4AO.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-48IN-16RL-4AO\Input_RCU_48IN_16RL_4AO.designer.cs">
      <DependentUpon>Input_RCU_48IN_16RL_4AO.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-48IN-16RL-4AO\IO_RCU_48IN_16RL_4AO.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-48IN-16RL-4AO\IO_RCU_48IN_16RL_4AO.designer.cs">
      <DependentUpon>IO_RCU_48IN_16RL_4AO.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-48IN-16RL\Input_RCU_48IN_16RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-48IN-16RL\Input_RCU_48IN_16RL.designer.cs">
      <DependentUpon>Input_RCU_48IN_16RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Board\RCU-48IN-16RL\IO_RCU_48IN_16RL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Board\RCU-48IN-16RL\IO_RCU_48IN_16RL.designer.cs">
      <DependentUpon>IO_RCU_48IN_16RL.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Report.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Report.designer.cs">
      <DependentUpon>Report.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Setup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Setup.designer.cs">
      <DependentUpon>Setup.cs</DependentUpon>
    </Compile>
    <Compile Include="RLCform.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RLCform.Designer.cs">
      <DependentUpon>RLCform.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="..\Common_Modules\AC_Out_Cfg.resx">
      <Link>Form\AC_Out_Cfg.resx</Link>
      <DependentUpon>AC_Out_Cfg.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Add Group.resx">
      <Link>Form\Add Group.resx</Link>
      <DependentUpon>Add Group.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Add_Unit.resx">
      <Link>Form\Add_Unit.resx</Link>
      <DependentUpon>Add_Unit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Backup.resx">
      <Link>Form\Backup.resx</Link>
      <DependentUpon>Backup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\Bedside 12 ports\Input_Bedside_12T.resx">
      <Link>Form\Board\Bedside 12 ports\Input_Bedside_12T.resx</Link>
      <DependentUpon>Input_Bedside_12T.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\Bedside 12 ports\IO_Bedside_12T.resx">
      <Link>Form\Board\Bedside 12 ports\IO_Bedside_12T.resx</Link>
      <DependentUpon>IO_Bedside_12T.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\Bedside\ConfigIO.resx">
      <Link>Form\Board\Bedside\ConfigIO.resx</Link>
      <DependentUpon>ConfigIO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\Bedside\I_O_Bedside.resx">
      <Link>Form\Board\Bedside\I_O_Bedside.resx</Link>
      <DependentUpon>I_O_Bedside.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\BSP_R14_OL\ConfigIO_BSP_R14_OL.resx">
      <Link>Form\Board\BSP_R14_OL\ConfigIO_BSP_R14_OL.resx</Link>
      <DependentUpon>ConfigIO_BSP_R14_OL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RCU-16RL-16AO\Input_RCU-16RL-16AO.resx">
      <Link>Form\Board\RCU-16RL-16AO\Input_RCU-16RL-16AO.resx</Link>
      <DependentUpon>Input_RCU-16RL-16AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RCU-16RL-16AO\IO_RCU-16RL-16AO.resx">
      <Link>Form\Board\RCU-16RL-16AO\IO_RCU-16RL-16AO.resx</Link>
      <DependentUpon>IO_RCU-16RL-16AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RCU-24RL-8AO\Input_RCU-24RL-8AO.resx">
      <Link>Form\Board\RCU-24RL-8AO\Input_RCU-24RL-8AO.resx</Link>
      <DependentUpon>Input_RCU-24RL-8AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RCU-24RL-8AO\IO_RCU-24RL-8AO.resx">
      <Link>Form\Board\RCU-24RL-8AO\IO_RCU-24RL-8AO.resx</Link>
      <DependentUpon>IO_RCU-24RL-8AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RCU-32AO\Input_RCU-32AO.resx">
      <Link>Form\Board\RCU-32AO\Input_RCU-32AO.resx</Link>
      <DependentUpon>Input_RCU-32AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RCU-32AO\IO_RCU-32AO.resx">
      <Link>Form\Board\RCU-32AO\IO_RCU-32AO.resx</Link>
      <DependentUpon>IO_RCU-32AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RCU-8RL-24AO\Input_RCU-8RL-24AO.resx">
      <Link>Form\Board\RCU-8RL-24AO\Input_RCU-8RL-24AO.resx</Link>
      <DependentUpon>Input_RCU-8RL-24AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RCU-8RL-24AO\IO_RCU-8RL-24AO.resx">
      <Link>Form\Board\RCU-8RL-24AO\IO_RCU-8RL-24AO.resx</Link>
      <DependentUpon>IO_RCU-8RL-24AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RLC_new 16 ports\Input_RLC_new.resx">
      <Link>Form\Board\RLC_new 16 ports\Input_RLC_new.resx</Link>
      <DependentUpon>Input_RLC_new.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RLC_new 16 ports\IO_RLC_new.resx">
      <Link>Form\Board\RLC_new 16 ports\IO_RLC_new.resx</Link>
      <DependentUpon>IO_RLC_new.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RLC_new 20 ports\Input_RLC_new_20_ports.resx">
      <Link>Form\Board\RLC_new 20 ports\Input_RLC_new_20_ports.resx</Link>
      <DependentUpon>Input_RLC_new_20_ports.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RLC_new 20 ports\IO_RLC_new_20_ports.resx">
      <Link>Form\Board\RLC_new 20 ports\IO_RLC_new_20_ports.resx</Link>
      <DependentUpon>IO_RLC_new_20_ports.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RLC_old\ConfigIO_RLC.resx">
      <Link>Form\Board\RLC_old\ConfigIO_RLC.resx</Link>
      <DependentUpon>ConfigIO_RLC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Board\RLC_old\Input_RLC.resx">
      <Link>Form\Board\RLC_old\Input_RLC.resx</Link>
      <DependentUpon>Input_RLC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\Light_Out_Cfg.resx">
      <Link>Form\Light_Out_Cfg.resx</Link>
      <DependentUpon>Light_Out_Cfg.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="..\Common_Modules\RS485_Config.resx">
      <Link>Form\RS485_Config.resx</Link>
      <DependentUpon>RS485_Config.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\BSP_R14_OL\Input_BSP_R14_OL.resx">
      <DependentUpon>Input_BSP_R14_OL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-ETH2SKDL\Input_GNT_ETH2SKDL.resx">
      <DependentUpon>Input_GNT_ETH2SKDL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-EXT-10AO\Input_GNT-EXT-10AO.resx">
      <DependentUpon>Input_GNT-EXT-10AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-EXT-12RL-12AO\Input_GNT-EXT-12RL-12AO.resx">
      <DependentUpon>Input_GNT-EXT-12RL-12AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-EXT-12RL\Input_GNT-EXT-12RL.resx">
      <DependentUpon>Input_GNT-EXT-12RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-EXT-20RL\Input_GNT-EXT-20RL.resx">
      <DependentUpon>Input_GNT-EXT-20RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-EXT-24IN\Input_GNT-EXT-24IN.resx">
      <DependentUpon>Input_GNT-EXT-24IN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-EXT-24IN\IO_GNT-EXT-24IN.resx">
      <DependentUpon>IO_GNT-EXT-24IN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-EXT-28AO\Input_GNT-EXT-28AO.resx">
      <DependentUpon>Input_GNT-EXT-28AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-EXT-48IN-T\Input_GNT-EXT-48IN-T.resx">
      <DependentUpon>Input_GNT-EXT-48IN-T.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-EXT-48IN-T\IO_GNT-EXT-48IN-T.resx">
      <DependentUpon>IO_GNT-EXT-48IN-T.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-EXT-8RL\Input_GNT-EXT-8RL.resx">
      <DependentUpon>Input_GNT-EXT-8RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\GNT-EXT-6RL\Input_GNT-EXT-6RL.resx">
      <DependentUpon>Input_GNT-EXT-6RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-11IN-4RL\Input_RCU_11IN_4RL.resx">
      <DependentUpon>Input_RCU_11IN_4RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-11IN-4RL\IO_RCU_11IN_4RL.resx">
      <DependentUpon>IO_RCU_11IN_4RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-21IN-10RL-T\Input_RCU_21IN_10RL_T.resx">
      <DependentUpon>Input_RCU_21IN_10RL_T.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-21IN-10RL-T\IO_RCU_21IN_10RL_T.resx">
      <DependentUpon>IO_RCU_21IN_10RL_T.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-21IN-10RL\Input_RCU_21IN_10RL.resx">
      <DependentUpon>Input_RCU_21IN_10RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-21IN-10RL\IO_RCU_21IN_10RL.resx">
      <DependentUpon>IO_RCU_21IN_10RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-21IN-8RL-K\Input_RCU_21IN_8RL_K.resx">
      <DependentUpon>Input_RCU_21IN_8RL_K.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-21IN-8RL-K\IO_RCU_21IN_8RL_K.resx">
      <DependentUpon>IO_RCU_21IN_8RL_K.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-21IN-8RL\Input_RCU_21IN_8RL.resx">
      <DependentUpon>Input_RCU_21IN_8RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-21IN-8RL\IO_RCU_21IN_8RL.resx">
      <DependentUpon>IO_RCU_21IN_8RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-30IN-10RL\Input_RCU_30IN_10RL.resx">
      <DependentUpon>Input_RCU_30IN_10RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-30IN-10RL\IO_RCU_30IN_10RL.resx">
      <DependentUpon>IO_RCU_30IN_10RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-48IN-16RL-4AO\Input_RCU_48IN_16RL_4AO.resx">
      <DependentUpon>Input_RCU_48IN_16RL_4AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-48IN-16RL-4AO\IO_RCU_48IN_16RL_4AO.resx">
      <DependentUpon>IO_RCU_48IN_16RL_4AO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-48IN-16RL\Input_RCU_48IN_16RL.resx">
      <DependentUpon>Input_RCU_48IN_16RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Board\RCU-48IN-16RL\IO_RCU_48IN_16RL.resx">
      <DependentUpon>IO_RCU_48IN_16RL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Report.resx">
      <DependentUpon>Report.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Setup.resx">
      <DependentUpon>Setup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="RLCform.resx">
      <DependentUpon>RLCform.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="app.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\renamer-5329-01.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\copy1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\add.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\renamer-5329-011.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\delete.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\bag.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\group.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\pj.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\unit.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\edit_128.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\edit_16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\add1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\config.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AddUnit.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\EditUnit.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ConfigUnit.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\DeleteUnit.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Addgroup.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Editgroup.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Deletegroup.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\paste-icon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\down.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\scan.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\software-develpment.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\scan1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\software-develpment1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\system-database-add-icon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\data-edit-icon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\updatefirmware.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\up.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\loff2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lon2.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Properties\app.manifest" />
    <None Include="Resources\lon2.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="TreeListMainDemo.ico" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>