﻿namespace RLC
{
    partial class RLC1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.TreeNode treeNode1 = new System.Windows.Forms.TreeNode("Project", 0, 0);
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(RLC1));
            this.treeView1 = new System.Windows.Forms.TreeView();
            this.panel1 = new System.Windows.Forms.Panel();
            this.label1 = new System.Windows.Forms.Label();
            this.panel3 = new System.Windows.Forms.Panel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.btn_EditUnitNetwork = new System.Windows.Forms.Button();
            this.btn_TranfertoData = new System.Windows.Forms.Button();
            this.btn_AddtoData = new System.Windows.Forms.Button();
            this.btn_Scan = new System.Windows.Forms.Button();
            this.btn_ConfigUnitNetwork = new System.Windows.Forms.Button();
            this.btn_ReplaceDatabase = new System.Windows.Forms.Button();
            this.btn_UpdateFirmware = new System.Windows.Forms.Button();
            this.panel4 = new System.Windows.Forms.Panel();
            this.label2 = new System.Windows.Forms.Label();
            this.grBoardNetwork = new System.Windows.Forms.DataGridView();
            this.SelectUnit = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewCheckBoxColumn1 = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.Fw_Ver = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.HW_Ver = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Man_Date = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Network_Chk_Recovery = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.gr_Unit = new System.Windows.Forms.DataGridView();
            this.Unit_Type = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Barcode = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gr_IPAddress = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ID_Can = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ActMode = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.LoadCan = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.db_fw_ver = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.db_Hw_Ver = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Descript = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DataBase_Chk_Recovery = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.gr_Group = new System.Windows.Forms.DataGridView();
            this.GroupName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Address = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Descripts = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.fileToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripRestoreProject = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripBackup = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.exitToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.editToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.copyProjectToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.pasteProjectToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.projectToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.addProjectAltAToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.renameProjectAltRToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.deleteProjectToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.updateFirmwareToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.setupToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.helpToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.aboutToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.fileToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.editToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.projectToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.hellpToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.exitToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStrip_Project = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem6 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem7 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem8 = new System.Windows.Forms.ToolStripMenuItem();
            this.addProJectToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStrip_Root = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tstripChild_copy = new System.Windows.Forms.ToolStripMenuItem();
            this.tstripChild_Paste = new System.Windows.Forms.ToolStripMenuItem();
            this.panel_Project = new System.Windows.Forms.Panel();
            this.btn_transferToNet = new System.Windows.Forms.Button();
            this.btn_Paste = new System.Windows.Forms.Button();
            this.btn_ConfigUnit = new System.Windows.Forms.Button();
            this.btn_AddGroup = new System.Windows.Forms.Button();
            this.btn_EditGroup = new System.Windows.Forms.Button();
            this.btn_DeleteGroup = new System.Windows.Forms.Button();
            this.btn_AddUnit = new System.Windows.Forms.Button();
            this.btn_EditUnit = new System.Windows.Forms.Button();
            this.btn_DeleteUnit = new System.Windows.Forms.Button();
            this.btn_AddProject = new System.Windows.Forms.Button();
            this.btn_RenameProject = new System.Windows.Forms.Button();
            this.btn_CopyProject = new System.Windows.Forms.Button();
            this.btn_DeleteProject = new System.Windows.Forms.Button();
            this.panel_firmware = new System.Windows.Forms.Panel();
            this.lbip = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.lb_percent = new System.Windows.Forms.Label();
            this.prBar_Update = new System.Windows.Forms.ProgressBar();
            this.styleController1 = new DevExpress.XtraEditors.StyleController(this.components);
            this.panel1.SuspendLayout();
            this.panel3.SuspendLayout();
            this.panel2.SuspendLayout();
            this.panel4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grBoardNetwork)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gr_Unit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gr_Group)).BeginInit();
            this.menuStrip1.SuspendLayout();
            this.contextMenuStrip_Project.SuspendLayout();
            this.contextMenuStrip_Root.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.panel_Project.SuspendLayout();
            this.panel_firmware.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.styleController1)).BeginInit();
            this.SuspendLayout();
            // 
            // treeView1
            // 
            this.treeView1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.treeView1.HideSelection = false;
            this.treeView1.Location = new System.Drawing.Point(1, 30);
            this.treeView1.Margin = new System.Windows.Forms.Padding(4);
            this.treeView1.Name = "treeView1";
            treeNode1.ImageIndex = 0;
            treeNode1.Name = "Project";
            treeNode1.SelectedImageIndex = 0;
            treeNode1.Text = "Project";
            this.treeView1.Nodes.AddRange(new System.Windows.Forms.TreeNode[] {
            treeNode1});
            this.treeView1.Size = new System.Drawing.Size(251, 819);
            this.treeView1.TabIndex = 0;
            this.treeView1.AfterLabelEdit += new System.Windows.Forms.NodeLabelEditEventHandler(this.treeView1_AfterLabelEdit);
            this.treeView1.AfterSelect += new System.Windows.Forms.TreeViewEventHandler(this.treeView1_AfterSelect);
            this.treeView1.NodeMouseClick += new System.Windows.Forms.TreeNodeMouseClickEventHandler(this.treeView1_NodeMouseClick);
            // 
            // panel1
            // 
            this.panel1.BackColor = System.Drawing.Color.DarkBlue;
            this.panel1.Controls.Add(this.label1);
            this.panel1.Location = new System.Drawing.Point(257, 30);
            this.panel1.Margin = new System.Windows.Forms.Padding(4);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1085, 30);
            this.panel1.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoEllipsis = true;
            this.label1.Font = new System.Drawing.Font("Microsoft Sans Serif", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.ForeColor = System.Drawing.SystemColors.ButtonHighlight;
            this.label1.Location = new System.Drawing.Point(15, -2);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(593, 33);
            this.label1.TabIndex = 0;
            this.label1.Text = "Room Controller";
            // 
            // panel3
            // 
            this.panel3.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel3.Controls.Add(this.panel2);
            this.panel3.Controls.Add(this.panel4);
            this.panel3.Controls.Add(this.grBoardNetwork);
            this.panel3.Controls.Add(this.gr_Unit);
            this.panel3.Controls.Add(this.gr_Group);
            this.panel3.Location = new System.Drawing.Point(257, 158);
            this.panel3.Margin = new System.Windows.Forms.Padding(4);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(1100, 692);
            this.panel3.TabIndex = 3;
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.btn_EditUnitNetwork);
            this.panel2.Controls.Add(this.btn_TranfertoData);
            this.panel2.Controls.Add(this.btn_AddtoData);
            this.panel2.Controls.Add(this.btn_Scan);
            this.panel2.Controls.Add(this.btn_ConfigUnitNetwork);
            this.panel2.Controls.Add(this.btn_ReplaceDatabase);
            this.panel2.Controls.Add(this.btn_UpdateFirmware);
            this.panel2.Location = new System.Drawing.Point(0, 288);
            this.panel2.Margin = new System.Windows.Forms.Padding(4);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(1084, 57);
            this.panel2.TabIndex = 5;
            this.panel2.Visible = false;
            // 
            // btn_EditUnitNetwork
            // 
            this.btn_EditUnitNetwork.BackColor = System.Drawing.Color.Transparent;
            this.btn_EditUnitNetwork.Enabled = false;
            this.btn_EditUnitNetwork.FlatAppearance.BorderSize = 0;
            this.btn_EditUnitNetwork.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_EditUnitNetwork.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_EditUnitNetwork.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_EditUnitNetwork.Image = global::RLC.Properties.Resources.EditUnit;
            this.btn_EditUnitNetwork.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_EditUnitNetwork.Location = new System.Drawing.Point(133, 4);
            this.btn_EditUnitNetwork.Margin = new System.Windows.Forms.Padding(4);
            this.btn_EditUnitNetwork.Name = "btn_EditUnitNetwork";
            this.btn_EditUnitNetwork.Size = new System.Drawing.Size(119, 49);
            this.btn_EditUnitNetwork.TabIndex = 32;
            this.btn_EditUnitNetwork.Text = "Edit Unit";
            this.btn_EditUnitNetwork.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_EditUnitNetwork.UseVisualStyleBackColor = false;
            this.btn_EditUnitNetwork.Click += new System.EventHandler(this.btn_EditUnitNetwork_Click);
            this.btn_EditUnitNetwork.MouseLeave += new System.EventHandler(this.btn_EditUnitNetwork_MouseLeave);
            this.btn_EditUnitNetwork.MouseHover += new System.EventHandler(this.btn_EditUnitNetwork_MouseHover);
            // 
            // btn_TranfertoData
            // 
            this.btn_TranfertoData.BackColor = System.Drawing.Color.Transparent;
            this.btn_TranfertoData.Enabled = false;
            this.btn_TranfertoData.FlatAppearance.BorderSize = 0;
            this.btn_TranfertoData.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_TranfertoData.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_TranfertoData.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_TranfertoData.Image = global::RLC.Properties.Resources.up;
            this.btn_TranfertoData.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_TranfertoData.Location = new System.Drawing.Point(715, 4);
            this.btn_TranfertoData.Margin = new System.Windows.Forms.Padding(4);
            this.btn_TranfertoData.Name = "btn_TranfertoData";
            this.btn_TranfertoData.Size = new System.Drawing.Size(200, 49);
            this.btn_TranfertoData.TabIndex = 31;
            this.btn_TranfertoData.Text = "Transfer All to Database";
            this.btn_TranfertoData.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_TranfertoData.UseVisualStyleBackColor = false;
            this.btn_TranfertoData.Click += new System.EventHandler(this.btn_TranfertoData_Click);
            this.btn_TranfertoData.MouseLeave += new System.EventHandler(this.btn_TranfertoData_MouseLeave);
            this.btn_TranfertoData.MouseHover += new System.EventHandler(this.btn_TranfertoData_MouseHover);
            // 
            // btn_AddtoData
            // 
            this.btn_AddtoData.BackColor = System.Drawing.Color.Transparent;
            this.btn_AddtoData.Enabled = false;
            this.btn_AddtoData.FlatAppearance.BorderSize = 0;
            this.btn_AddtoData.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_AddtoData.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_AddtoData.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_AddtoData.Image = global::RLC.Properties.Resources.system_database_add_icon;
            this.btn_AddtoData.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_AddtoData.Location = new System.Drawing.Point(396, 4);
            this.btn_AddtoData.Margin = new System.Windows.Forms.Padding(4);
            this.btn_AddtoData.Name = "btn_AddtoData";
            this.btn_AddtoData.Size = new System.Drawing.Size(141, 49);
            this.btn_AddtoData.TabIndex = 30;
            this.btn_AddtoData.Text = "Add to Database";
            this.btn_AddtoData.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_AddtoData.UseVisualStyleBackColor = false;
            this.btn_AddtoData.Click += new System.EventHandler(this.btn_AddtoData_Click);
            this.btn_AddtoData.MouseLeave += new System.EventHandler(this.btn_AddtoData_MouseLeave);
            this.btn_AddtoData.MouseHover += new System.EventHandler(this.btn_AddtoData_MouseHover);
            // 
            // btn_Scan
            // 
            this.btn_Scan.BackColor = System.Drawing.Color.Transparent;
            this.btn_Scan.FlatAppearance.BorderSize = 0;
            this.btn_Scan.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_Scan.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Scan.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Scan.Image = global::RLC.Properties.Resources.scan1;
            this.btn_Scan.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_Scan.Location = new System.Drawing.Point(4, 4);
            this.btn_Scan.Margin = new System.Windows.Forms.Padding(4);
            this.btn_Scan.Name = "btn_Scan";
            this.btn_Scan.Size = new System.Drawing.Size(121, 49);
            this.btn_Scan.TabIndex = 29;
            this.btn_Scan.Text = "Scan Network";
            this.btn_Scan.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_Scan.UseVisualStyleBackColor = false;
            this.btn_Scan.Click += new System.EventHandler(this.btn_Scan_Click);
            this.btn_Scan.MouseLeave += new System.EventHandler(this.btn_Scan_MouseLeave);
            this.btn_Scan.MouseHover += new System.EventHandler(this.btn_Scan_MouseHover);
            // 
            // btn_ConfigUnitNetwork
            // 
            this.btn_ConfigUnitNetwork.BackColor = System.Drawing.Color.Transparent;
            this.btn_ConfigUnitNetwork.Enabled = false;
            this.btn_ConfigUnitNetwork.FlatAppearance.BorderSize = 0;
            this.btn_ConfigUnitNetwork.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_ConfigUnitNetwork.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_ConfigUnitNetwork.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_ConfigUnitNetwork.Image = global::RLC.Properties.Resources.ConfigUnit;
            this.btn_ConfigUnitNetwork.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_ConfigUnitNetwork.Location = new System.Drawing.Point(261, 4);
            this.btn_ConfigUnitNetwork.Margin = new System.Windows.Forms.Padding(4);
            this.btn_ConfigUnitNetwork.Name = "btn_ConfigUnitNetwork";
            this.btn_ConfigUnitNetwork.Size = new System.Drawing.Size(123, 49);
            this.btn_ConfigUnitNetwork.TabIndex = 27;
            this.btn_ConfigUnitNetwork.Text = "Config I/O";
            this.btn_ConfigUnitNetwork.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_ConfigUnitNetwork.UseVisualStyleBackColor = false;
            this.btn_ConfigUnitNetwork.Click += new System.EventHandler(this.btn_ConfigUnitNetwork_Click);
            this.btn_ConfigUnitNetwork.MouseLeave += new System.EventHandler(this.btn_ConfigUnitNetwork_MouseLeave);
            this.btn_ConfigUnitNetwork.MouseHover += new System.EventHandler(this.btn_ConfigUnitNetwork_MouseHover);
            // 
            // btn_ReplaceDatabase
            // 
            this.btn_ReplaceDatabase.BackColor = System.Drawing.Color.Transparent;
            this.btn_ReplaceDatabase.Enabled = false;
            this.btn_ReplaceDatabase.FlatAppearance.BorderSize = 0;
            this.btn_ReplaceDatabase.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_ReplaceDatabase.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_ReplaceDatabase.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_ReplaceDatabase.Image = global::RLC.Properties.Resources.data_edit_icon;
            this.btn_ReplaceDatabase.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_ReplaceDatabase.Location = new System.Drawing.Point(549, 4);
            this.btn_ReplaceDatabase.Margin = new System.Windows.Forms.Padding(4);
            this.btn_ReplaceDatabase.Name = "btn_ReplaceDatabase";
            this.btn_ReplaceDatabase.Size = new System.Drawing.Size(157, 49);
            this.btn_ReplaceDatabase.TabIndex = 28;
            this.btn_ReplaceDatabase.Text = "Replace Database";
            this.btn_ReplaceDatabase.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_ReplaceDatabase.UseVisualStyleBackColor = false;
            this.btn_ReplaceDatabase.Click += new System.EventHandler(this.btn_ReplaceDatabase_Click);
            this.btn_ReplaceDatabase.MouseLeave += new System.EventHandler(this.btn_ReplaceDatabase_MouseLeave);
            this.btn_ReplaceDatabase.MouseHover += new System.EventHandler(this.btn_ReplaceDatabase_MouseHover);
            // 
            // btn_UpdateFirmware
            // 
            this.btn_UpdateFirmware.BackColor = System.Drawing.Color.Transparent;
            this.btn_UpdateFirmware.Enabled = false;
            this.btn_UpdateFirmware.FlatAppearance.BorderSize = 0;
            this.btn_UpdateFirmware.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_UpdateFirmware.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_UpdateFirmware.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_UpdateFirmware.Image = global::RLC.Properties.Resources.updatefirmware;
            this.btn_UpdateFirmware.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_UpdateFirmware.Location = new System.Drawing.Point(923, 4);
            this.btn_UpdateFirmware.Margin = new System.Windows.Forms.Padding(4);
            this.btn_UpdateFirmware.Name = "btn_UpdateFirmware";
            this.btn_UpdateFirmware.Size = new System.Drawing.Size(157, 49);
            this.btn_UpdateFirmware.TabIndex = 26;
            this.btn_UpdateFirmware.Text = "Update Firmware";
            this.btn_UpdateFirmware.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_UpdateFirmware.UseVisualStyleBackColor = false;
            this.btn_UpdateFirmware.Click += new System.EventHandler(this.btn_UpdateFirmware_Click);
            this.btn_UpdateFirmware.MouseLeave += new System.EventHandler(this.btn_UpdateFirmware_MouseLeave);
            this.btn_UpdateFirmware.MouseHover += new System.EventHandler(this.btn_UpdateFirmware_MouseHover);
            // 
            // panel4
            // 
            this.panel4.BackColor = System.Drawing.Color.DarkBlue;
            this.panel4.Controls.Add(this.label2);
            this.panel4.Location = new System.Drawing.Point(0, 260);
            this.panel4.Margin = new System.Windows.Forms.Padding(4);
            this.panel4.Name = "panel4";
            this.panel4.Size = new System.Drawing.Size(1085, 30);
            this.panel4.TabIndex = 4;
            this.panel4.Visible = false;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("Microsoft Sans Serif", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.ForeColor = System.Drawing.SystemColors.ButtonHighlight;
            this.label2.Location = new System.Drawing.Point(15, 0);
            this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(212, 29);
            this.label2.TabIndex = 0;
            this.label2.Text = "Units on Network";
            // 
            // grBoardNetwork
            // 
            this.grBoardNetwork.AllowUserToAddRows = false;
            this.grBoardNetwork.AllowUserToDeleteRows = false;
            this.grBoardNetwork.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.grBoardNetwork.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.AllCellsExceptHeaders;
            this.grBoardNetwork.BackgroundColor = System.Drawing.SystemColors.ButtonHighlight;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.grBoardNetwork.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.grBoardNetwork.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.grBoardNetwork.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.SelectUnit,
            this.dataGridViewTextBoxColumn1,
            this.dataGridViewTextBoxColumn2,
            this.dataGridViewTextBoxColumn3,
            this.dataGridViewTextBoxColumn4,
            this.dataGridViewTextBoxColumn5,
            this.dataGridViewCheckBoxColumn1,
            this.Fw_Ver,
            this.HW_Ver,
            this.dataGridViewTextBoxColumn6,
            this.Man_Date,
            this.Network_Chk_Recovery});
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.grBoardNetwork.DefaultCellStyle = dataGridViewCellStyle2;
            this.grBoardNetwork.Location = new System.Drawing.Point(0, 346);
            this.grBoardNetwork.Margin = new System.Windows.Forms.Padding(4);
            this.grBoardNetwork.MultiSelect = false;
            this.grBoardNetwork.Name = "grBoardNetwork";
            this.grBoardNetwork.ReadOnly = true;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.grBoardNetwork.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.grBoardNetwork.RowHeadersWidth = 4;
            this.grBoardNetwork.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.grBoardNetwork.Size = new System.Drawing.Size(1085, 342);
            this.grBoardNetwork.TabIndex = 2;
            this.grBoardNetwork.Visible = false;
            this.grBoardNetwork.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.DataGridView_CellClick);
            this.grBoardNetwork.RowPostPaint += new System.Windows.Forms.DataGridViewRowPostPaintEventHandler(this.grBoardNetwork_RowPostPaint);
            this.grBoardNetwork.SortCompare += new System.Windows.Forms.DataGridViewSortCompareEventHandler(this.grBoardNetwork_SortCompare);
            // 
            // SelectUnit
            // 
            this.SelectUnit.HeaderText = "Select";
            this.SelectUnit.Name = "SelectUnit";
            this.SelectUnit.ReadOnly = true;
            this.SelectUnit.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.SelectUnit.Width = 45;
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.HeaderText = "Unit Type";
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            this.dataGridViewTextBoxColumn1.ReadOnly = true;
            this.dataGridViewTextBoxColumn1.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewTextBoxColumn1.Width = 140;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.HeaderText = "Serial No.";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            this.dataGridViewTextBoxColumn2.ReadOnly = true;
            this.dataGridViewTextBoxColumn2.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewTextBoxColumn2.Width = 120;
            // 
            // dataGridViewTextBoxColumn3
            // 
            this.dataGridViewTextBoxColumn3.HeaderText = "IP Address";
            this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            this.dataGridViewTextBoxColumn3.ReadOnly = true;
            this.dataGridViewTextBoxColumn3.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewTextBoxColumn3.Width = 110;
            // 
            // dataGridViewTextBoxColumn4
            // 
            this.dataGridViewTextBoxColumn4.HeaderText = "ID CAN";
            this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
            this.dataGridViewTextBoxColumn4.ReadOnly = true;
            this.dataGridViewTextBoxColumn4.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewTextBoxColumn4.Width = 95;
            // 
            // dataGridViewTextBoxColumn5
            // 
            this.dataGridViewTextBoxColumn5.HeaderText = "Act Mode";
            this.dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
            this.dataGridViewTextBoxColumn5.ReadOnly = true;
            this.dataGridViewTextBoxColumn5.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewTextBoxColumn5.Width = 95;
            // 
            // dataGridViewCheckBoxColumn1
            // 
            this.dataGridViewCheckBoxColumn1.HeaderText = "CAN Load";
            this.dataGridViewCheckBoxColumn1.Name = "dataGridViewCheckBoxColumn1";
            this.dataGridViewCheckBoxColumn1.ReadOnly = true;
            this.dataGridViewCheckBoxColumn1.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewCheckBoxColumn1.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic;
            // 
            // Fw_Ver
            // 
            this.Fw_Ver.HeaderText = "Firmware Ver";
            this.Fw_Ver.Name = "Fw_Ver";
            this.Fw_Ver.ReadOnly = true;
            this.Fw_Ver.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.Fw_Ver.Width = 120;
            // 
            // HW_Ver
            // 
            this.HW_Ver.HeaderText = "BHW";
            this.HW_Ver.Name = "HW_Ver";
            this.HW_Ver.ReadOnly = true;
            this.HW_Ver.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.HW_Ver.Width = 50;
            // 
            // dataGridViewTextBoxColumn6
            // 
            this.dataGridViewTextBoxColumn6.HeaderText = "Description";
            this.dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
            this.dataGridViewTextBoxColumn6.ReadOnly = true;
            this.dataGridViewTextBoxColumn6.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewTextBoxColumn6.Width = 170;
            // 
            // Man_Date
            // 
            this.Man_Date.HeaderText = "Man_Date";
            this.Man_Date.Name = "Man_Date";
            this.Man_Date.ReadOnly = true;
            this.Man_Date.Visible = false;
            // 
            // Network_Chk_Recovery
            // 
            this.Network_Chk_Recovery.HeaderText = "Recovery";
            this.Network_Chk_Recovery.Name = "Network_Chk_Recovery";
            this.Network_Chk_Recovery.ReadOnly = true;
            this.Network_Chk_Recovery.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.Network_Chk_Recovery.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic;
            this.Network_Chk_Recovery.Visible = false;
            // 
            // gr_Unit
            // 
            this.gr_Unit.AllowUserToAddRows = false;
            this.gr_Unit.BackgroundColor = System.Drawing.SystemColors.ButtonHighlight;
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.gr_Unit.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.gr_Unit.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.gr_Unit.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.Unit_Type,
            this.Barcode,
            this.gr_IPAddress,
            this.ID_Can,
            this.ActMode,
            this.LoadCan,
            this.db_fw_ver,
            this.db_Hw_Ver,
            this.Descript,
            this.DataBase_Chk_Recovery});
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle5.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle5.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle5.SelectionBackColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.gr_Unit.DefaultCellStyle = dataGridViewCellStyle5;
            this.gr_Unit.Location = new System.Drawing.Point(-1, 1);
            this.gr_Unit.Margin = new System.Windows.Forms.Padding(4);
            this.gr_Unit.MultiSelect = false;
            this.gr_Unit.Name = "gr_Unit";
            this.gr_Unit.ReadOnly = true;
            this.gr_Unit.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle6.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle6.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle6.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.gr_Unit.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            this.gr_Unit.RowHeadersWidth = 45;
            this.gr_Unit.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.SystemColors.ControlText;
            this.gr_Unit.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.gr_Unit.Size = new System.Drawing.Size(1085, 204);
            this.gr_Unit.TabIndex = 1;
            this.gr_Unit.Visible = false;
            this.gr_Unit.RowPostPaint += new System.Windows.Forms.DataGridViewRowPostPaintEventHandler(this.gr_Unit_RowPostPaint);
            this.gr_Unit.RowsAdded += new System.Windows.Forms.DataGridViewRowsAddedEventHandler(this.gr_Unit_RowsAdded);
            this.gr_Unit.RowsRemoved += new System.Windows.Forms.DataGridViewRowsRemovedEventHandler(this.gr_Unit_RowsRemoved);
            this.gr_Unit.UserDeletingRow += new System.Windows.Forms.DataGridViewRowCancelEventHandler(this.gr_Unit_UserDeletingRow);
            // 
            // Unit_Type
            // 
            this.Unit_Type.HeaderText = "Unit Type";
            this.Unit_Type.Name = "Unit_Type";
            this.Unit_Type.ReadOnly = true;
            this.Unit_Type.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.Unit_Type.Width = 140;
            // 
            // Barcode
            // 
            this.Barcode.HeaderText = "Serial No.";
            this.Barcode.Name = "Barcode";
            this.Barcode.ReadOnly = true;
            this.Barcode.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.Barcode.Width = 120;
            // 
            // gr_IPAddress
            // 
            this.gr_IPAddress.HeaderText = "IP Address";
            this.gr_IPAddress.Name = "gr_IPAddress";
            this.gr_IPAddress.ReadOnly = true;
            this.gr_IPAddress.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.gr_IPAddress.Width = 110;
            // 
            // ID_Can
            // 
            this.ID_Can.HeaderText = "ID CAN";
            this.ID_Can.Name = "ID_Can";
            this.ID_Can.ReadOnly = true;
            this.ID_Can.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.ID_Can.Width = 95;
            // 
            // ActMode
            // 
            this.ActMode.HeaderText = "Act Mode";
            this.ActMode.Name = "ActMode";
            this.ActMode.ReadOnly = true;
            this.ActMode.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.ActMode.Width = 95;
            // 
            // LoadCan
            // 
            this.LoadCan.HeaderText = "CAN Load";
            this.LoadCan.Name = "LoadCan";
            this.LoadCan.ReadOnly = true;
            this.LoadCan.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.LoadCan.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic;
            // 
            // db_fw_ver
            // 
            this.db_fw_ver.HeaderText = "Firmware Ver";
            this.db_fw_ver.Name = "db_fw_ver";
            this.db_fw_ver.ReadOnly = true;
            this.db_fw_ver.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.db_fw_ver.Width = 120;
            // 
            // db_Hw_Ver
            // 
            this.db_Hw_Ver.HeaderText = "BHW";
            this.db_Hw_Ver.Name = "db_Hw_Ver";
            this.db_Hw_Ver.ReadOnly = true;
            this.db_Hw_Ver.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.db_Hw_Ver.Width = 50;
            // 
            // Descript
            // 
            this.Descript.HeaderText = "Description";
            this.Descript.Name = "Descript";
            this.Descript.ReadOnly = true;
            this.Descript.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.Descript.Width = 170;
            // 
            // DataBase_Chk_Recovery
            // 
            this.DataBase_Chk_Recovery.HeaderText = "Recovery";
            this.DataBase_Chk_Recovery.Name = "DataBase_Chk_Recovery";
            this.DataBase_Chk_Recovery.ReadOnly = true;
            this.DataBase_Chk_Recovery.Resizable = System.Windows.Forms.DataGridViewTriState.False;
            this.DataBase_Chk_Recovery.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic;
            this.DataBase_Chk_Recovery.Visible = false;
            // 
            // gr_Group
            // 
            this.gr_Group.AllowUserToAddRows = false;
            this.gr_Group.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.gr_Group.BackgroundColor = System.Drawing.SystemColors.ControlLightLight;
            this.gr_Group.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.gr_Group.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.GroupName,
            this.Address,
            this.Descripts});
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle7.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle7.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle7.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.gr_Group.DefaultCellStyle = dataGridViewCellStyle7;
            this.gr_Group.Location = new System.Drawing.Point(-1, 1);
            this.gr_Group.Margin = new System.Windows.Forms.Padding(4);
            this.gr_Group.MultiSelect = false;
            this.gr_Group.Name = "gr_Group";
            this.gr_Group.ReadOnly = true;
            this.gr_Group.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.gr_Group.Size = new System.Drawing.Size(1085, 687);
            this.gr_Group.TabIndex = 0;
            this.gr_Group.RowPostPaint += new System.Windows.Forms.DataGridViewRowPostPaintEventHandler(this.gr_Group_RowPostPaint);
            this.gr_Group.RowsRemoved += new System.Windows.Forms.DataGridViewRowsRemovedEventHandler(this.gr_Group_RowsRemoved);
            this.gr_Group.UserDeletingRow += new System.Windows.Forms.DataGridViewRowCancelEventHandler(this.gr_Group_UserDeletingRow);
            // 
            // GroupName
            // 
            this.GroupName.HeaderText = "Group Name";
            this.GroupName.Name = "GroupName";
            this.GroupName.ReadOnly = true;
            this.GroupName.Width = 250;
            // 
            // Address
            // 
            this.Address.HeaderText = "Address";
            this.Address.Name = "Address";
            this.Address.ReadOnly = true;
            // 
            // Descripts
            // 
            this.Descripts.HeaderText = "Description";
            this.Descripts.Name = "Descripts";
            this.Descripts.ReadOnly = true;
            this.Descripts.Width = 404;
            // 
            // menuStrip1
            // 
            this.menuStrip1.BackColor = System.Drawing.Color.DarkGray;
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.fileToolStripMenuItem1,
            this.editToolStripMenuItem1,
            this.projectToolStripMenuItem1,
            this.toolToolStripMenuItem1,
            this.helpToolStripMenuItem});
            this.menuStrip1.Location = new System.Drawing.Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Padding = new System.Windows.Forms.Padding(0, 2, 0, 2);
            this.menuStrip1.RenderMode = System.Windows.Forms.ToolStripRenderMode.Professional;
            this.menuStrip1.Size = new System.Drawing.Size(1341, 33);
            this.menuStrip1.TabIndex = 4;
            this.menuStrip1.Text = "menuStrip1";
            // 
            // fileToolStripMenuItem1
            // 
            this.fileToolStripMenuItem1.Checked = true;
            this.fileToolStripMenuItem1.CheckState = System.Windows.Forms.CheckState.Checked;
            this.fileToolStripMenuItem1.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripRestoreProject,
            this.toolStripBackup,
            this.toolStripSeparator1,
            this.exitToolStripMenuItem1});
            this.fileToolStripMenuItem1.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.fileToolStripMenuItem1.Name = "fileToolStripMenuItem1";
            this.fileToolStripMenuItem1.ShortcutKeyDisplayString = "_";
            this.fileToolStripMenuItem1.Size = new System.Drawing.Size(44, 29);
            this.fileToolStripMenuItem1.Text = "&File";
            // 
            // toolStripRestoreProject
            // 
            this.toolStripRestoreProject.Name = "toolStripRestoreProject";
            this.toolStripRestoreProject.Size = new System.Drawing.Size(187, 24);
            this.toolStripRestoreProject.Text = "&Restore Project...";
            this.toolStripRestoreProject.Click += new System.EventHandler(this.toolStripRestoreProject_Click);
            // 
            // toolStripBackup
            // 
            this.toolStripBackup.Name = "toolStripBackup";
            this.toolStripBackup.Size = new System.Drawing.Size(187, 24);
            this.toolStripBackup.Text = "&Backup Project...";
            this.toolStripBackup.Click += new System.EventHandler(this.toolStripBackup_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(184, 6);
            // 
            // exitToolStripMenuItem1
            // 
            this.exitToolStripMenuItem1.Name = "exitToolStripMenuItem1";
            this.exitToolStripMenuItem1.Size = new System.Drawing.Size(187, 24);
            this.exitToolStripMenuItem1.Text = "E&xit";
            this.exitToolStripMenuItem1.Click += new System.EventHandler(this.exitToolStripMenuItem1_Click);
            // 
            // editToolStripMenuItem1
            // 
            this.editToolStripMenuItem1.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.copyProjectToolStripMenuItem,
            this.pasteProjectToolStripMenuItem});
            this.editToolStripMenuItem1.Name = "editToolStripMenuItem1";
            this.editToolStripMenuItem1.Size = new System.Drawing.Size(54, 29);
            this.editToolStripMenuItem1.Text = "&Edit";
            // 
            // copyProjectToolStripMenuItem
            // 
            this.copyProjectToolStripMenuItem.Enabled = false;
            this.copyProjectToolStripMenuItem.Name = "copyProjectToolStripMenuItem";
            this.copyProjectToolStripMenuItem.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.C)));
            this.copyProjectToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.copyProjectToolStripMenuItem.Text = "&Copy";
            this.copyProjectToolStripMenuItem.Click += new System.EventHandler(this.btn_CopyProject_Click);
            // 
            // pasteProjectToolStripMenuItem
            // 
            this.pasteProjectToolStripMenuItem.Enabled = false;
            this.pasteProjectToolStripMenuItem.Name = "pasteProjectToolStripMenuItem";
            this.pasteProjectToolStripMenuItem.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.V)));
            this.pasteProjectToolStripMenuItem.Size = new System.Drawing.Size(188, 30);
            this.pasteProjectToolStripMenuItem.Text = "&Paste";
            this.pasteProjectToolStripMenuItem.Click += new System.EventHandler(this.pasteProjectToolStripMenuItem_Click);
            // 
            // projectToolStripMenuItem1
            // 
            this.projectToolStripMenuItem1.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addProjectAltAToolStripMenuItem,
            this.renameProjectAltRToolStripMenuItem,
            this.deleteProjectToolStripMenuItem});
            this.projectToolStripMenuItem1.Name = "projectToolStripMenuItem1";
            this.projectToolStripMenuItem1.Size = new System.Drawing.Size(78, 29);
            this.projectToolStripMenuItem1.Text = "&Project";
            // 
            // addProjectAltAToolStripMenuItem
            // 
            this.addProjectAltAToolStripMenuItem.Name = "addProjectAltAToolStripMenuItem";
            this.addProjectAltAToolStripMenuItem.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Alt | System.Windows.Forms.Keys.A)));
            this.addProjectAltAToolStripMenuItem.Size = new System.Drawing.Size(240, 30);
            this.addProjectAltAToolStripMenuItem.Text = "&Add Project ";
            this.addProjectAltAToolStripMenuItem.Click += new System.EventHandler(this.btn_AddProject_Click);
            // 
            // renameProjectAltRToolStripMenuItem
            // 
            this.renameProjectAltRToolStripMenuItem.Enabled = false;
            this.renameProjectAltRToolStripMenuItem.Name = "renameProjectAltRToolStripMenuItem";
            this.renameProjectAltRToolStripMenuItem.ShortcutKeys = System.Windows.Forms.Keys.F2;
            this.renameProjectAltRToolStripMenuItem.Size = new System.Drawing.Size(240, 30);
            this.renameProjectAltRToolStripMenuItem.Text = "&Rename Project";
            this.renameProjectAltRToolStripMenuItem.Click += new System.EventHandler(this.btn_RenameProject_Click);
            // 
            // deleteProjectToolStripMenuItem
            // 
            this.deleteProjectToolStripMenuItem.Enabled = false;
            this.deleteProjectToolStripMenuItem.Name = "deleteProjectToolStripMenuItem";
            this.deleteProjectToolStripMenuItem.ShortcutKeys = System.Windows.Forms.Keys.Delete;
            this.deleteProjectToolStripMenuItem.Size = new System.Drawing.Size(240, 30);
            this.deleteProjectToolStripMenuItem.Text = "&Delete Project";
            this.deleteProjectToolStripMenuItem.Click += new System.EventHandler(this.btn_DeleteProject_Click);
            // 
            // toolToolStripMenuItem1
            // 
            this.toolToolStripMenuItem1.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.updateFirmwareToolStripMenuItem,
            this.setupToolStripMenuItem});
            this.toolToolStripMenuItem1.Name = "toolToolStripMenuItem1";
            this.toolToolStripMenuItem1.Size = new System.Drawing.Size(57, 29);
            this.toolToolStripMenuItem1.Text = "&Tool";
            // 
            // updateFirmwareToolStripMenuItem
            // 
            this.updateFirmwareToolStripMenuItem.Enabled = false;
            this.updateFirmwareToolStripMenuItem.Name = "updateFirmwareToolStripMenuItem";
            this.updateFirmwareToolStripMenuItem.Size = new System.Drawing.Size(231, 30);
            this.updateFirmwareToolStripMenuItem.Text = "Update Firmware...";
            this.updateFirmwareToolStripMenuItem.Click += new System.EventHandler(this.btn_UpdateFirmware_Click);
            // 
            // setupToolStripMenuItem
            // 
            this.setupToolStripMenuItem.Name = "setupToolStripMenuItem";
            this.setupToolStripMenuItem.Size = new System.Drawing.Size(231, 30);
            this.setupToolStripMenuItem.Text = "&Setup...";
            this.setupToolStripMenuItem.Click += new System.EventHandler(this.setupToolStripMenuItem_Click);
            // 
            // helpToolStripMenuItem
            // 
            this.helpToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.aboutToolStripMenuItem});
            this.helpToolStripMenuItem.Name = "helpToolStripMenuItem";
            this.helpToolStripMenuItem.Size = new System.Drawing.Size(61, 29);
            this.helpToolStripMenuItem.Text = "&Help";
            // 
            // aboutToolStripMenuItem
            // 
            this.aboutToolStripMenuItem.Name = "aboutToolStripMenuItem";
            this.aboutToolStripMenuItem.Size = new System.Drawing.Size(146, 30);
            this.aboutToolStripMenuItem.Text = "&About...";
            // 
            // fileToolStripMenuItem
            // 
            this.fileToolStripMenuItem.Name = "fileToolStripMenuItem";
            this.fileToolStripMenuItem.Size = new System.Drawing.Size(37, 20);
            this.fileToolStripMenuItem.Text = "File";
            // 
            // editToolStripMenuItem
            // 
            this.editToolStripMenuItem.Name = "editToolStripMenuItem";
            this.editToolStripMenuItem.Size = new System.Drawing.Size(39, 20);
            this.editToolStripMenuItem.Text = "Edit";
            // 
            // projectToolStripMenuItem
            // 
            this.projectToolStripMenuItem.Name = "projectToolStripMenuItem";
            this.projectToolStripMenuItem.Size = new System.Drawing.Size(56, 20);
            this.projectToolStripMenuItem.Text = "Project";
            // 
            // toolToolStripMenuItem
            // 
            this.toolToolStripMenuItem.Name = "toolToolStripMenuItem";
            this.toolToolStripMenuItem.Size = new System.Drawing.Size(43, 20);
            this.toolToolStripMenuItem.Text = "Tool";
            // 
            // hellpToolStripMenuItem
            // 
            this.hellpToolStripMenuItem.Name = "hellpToolStripMenuItem";
            this.hellpToolStripMenuItem.Size = new System.Drawing.Size(44, 20);
            this.hellpToolStripMenuItem.Text = "Help";
            // 
            // exitToolStripMenuItem
            // 
            this.exitToolStripMenuItem.Name = "exitToolStripMenuItem";
            this.exitToolStripMenuItem.Size = new System.Drawing.Size(152, 22);
            this.exitToolStripMenuItem.Text = "Exit";
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(180, 22);
            this.toolStripMenuItem1.Text = "toolStripMenuItem1";
            // 
            // contextMenuStrip_Project
            // 
            this.contextMenuStrip_Project.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem4,
            this.toolStripMenuItem5,
            this.toolStripMenuItem6,
            this.toolStripMenuItem7,
            this.toolStripMenuItem8});
            this.contextMenuStrip_Project.Name = "contextMenuStrip1";
            this.contextMenuStrip_Project.Size = new System.Drawing.Size(327, 154);
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(326, 30);
            this.toolStripMenuItem4.Text = "Add Project                   Alt+A";
            this.toolStripMenuItem4.Click += new System.EventHandler(this.btn_AddProject_Click);
            // 
            // toolStripMenuItem5
            // 
            this.toolStripMenuItem5.Name = "toolStripMenuItem5";
            this.toolStripMenuItem5.Size = new System.Drawing.Size(326, 30);
            this.toolStripMenuItem5.Text = "Copy                              Ctrl+C";
            this.toolStripMenuItem5.Click += new System.EventHandler(this.btn_CopyProject_Click);
            // 
            // toolStripMenuItem6
            // 
            this.toolStripMenuItem6.Enabled = false;
            this.toolStripMenuItem6.Name = "toolStripMenuItem6";
            this.toolStripMenuItem6.Size = new System.Drawing.Size(326, 30);
            this.toolStripMenuItem6.Text = "Paste                              Ctrl+V";
            this.toolStripMenuItem6.Click += new System.EventHandler(this.pasteProjectToolStripMenuItem_Click);
            // 
            // toolStripMenuItem7
            // 
            this.toolStripMenuItem7.Name = "toolStripMenuItem7";
            this.toolStripMenuItem7.Size = new System.Drawing.Size(326, 30);
            this.toolStripMenuItem7.Text = "Rename Project            Alt+R";
            this.toolStripMenuItem7.Click += new System.EventHandler(this.btn_RenameProject_Click);
            // 
            // toolStripMenuItem8
            // 
            this.toolStripMenuItem8.Name = "toolStripMenuItem8";
            this.toolStripMenuItem8.Size = new System.Drawing.Size(326, 30);
            this.toolStripMenuItem8.Text = "Delete Project               Alt+D";
            this.toolStripMenuItem8.Click += new System.EventHandler(this.btn_DeleteProject_Click);
            // 
            // addProJectToolStripMenuItem
            // 
            this.addProJectToolStripMenuItem.Name = "addProJectToolStripMenuItem";
            this.addProJectToolStripMenuItem.Size = new System.Drawing.Size(177, 30);
            this.addProJectToolStripMenuItem.Text = "Add Project";
            this.addProJectToolStripMenuItem.Click += new System.EventHandler(this.btn_AddProject_Click);
            // 
            // contextMenuStrip_Root
            // 
            this.contextMenuStrip_Root.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addProJectToolStripMenuItem});
            this.contextMenuStrip_Root.Name = "contextMenuStrip1";
            this.contextMenuStrip_Root.Size = new System.Drawing.Size(178, 34);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tstripChild_copy,
            this.tstripChild_Paste});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(257, 64);
            // 
            // tstripChild_copy
            // 
            this.tstripChild_copy.Name = "tstripChild_copy";
            this.tstripChild_copy.Size = new System.Drawing.Size(256, 30);
            this.tstripChild_copy.Text = "Copy                Ctrl+C";
            this.tstripChild_copy.Click += new System.EventHandler(this.btn_CopyProject_Click);
            // 
            // tstripChild_Paste
            // 
            this.tstripChild_Paste.Enabled = false;
            this.tstripChild_Paste.Name = "tstripChild_Paste";
            this.tstripChild_Paste.Size = new System.Drawing.Size(256, 30);
            this.tstripChild_Paste.Text = "Paste                Ctrl+V";
            this.tstripChild_Paste.Click += new System.EventHandler(this.pasteProjectToolStripMenuItem_Click);
            // 
            // panel_Project
            // 
            this.panel_Project.Controls.Add(this.btn_transferToNet);
            this.panel_Project.Controls.Add(this.btn_Paste);
            this.panel_Project.Controls.Add(this.btn_ConfigUnit);
            this.panel_Project.Controls.Add(this.btn_AddGroup);
            this.panel_Project.Controls.Add(this.btn_EditGroup);
            this.panel_Project.Controls.Add(this.btn_DeleteGroup);
            this.panel_Project.Controls.Add(this.btn_AddUnit);
            this.panel_Project.Controls.Add(this.btn_EditUnit);
            this.panel_Project.Controls.Add(this.btn_DeleteUnit);
            this.panel_Project.Controls.Add(this.btn_AddProject);
            this.panel_Project.Controls.Add(this.btn_RenameProject);
            this.panel_Project.Controls.Add(this.btn_CopyProject);
            this.panel_Project.Controls.Add(this.btn_DeleteProject);
            this.panel_Project.Location = new System.Drawing.Point(257, 54);
            this.panel_Project.Margin = new System.Windows.Forms.Padding(4);
            this.panel_Project.Name = "panel_Project";
            this.panel_Project.Size = new System.Drawing.Size(1085, 107);
            this.panel_Project.TabIndex = 2;
            // 
            // btn_transferToNet
            // 
            this.btn_transferToNet.BackColor = System.Drawing.Color.Transparent;
            this.btn_transferToNet.Enabled = false;
            this.btn_transferToNet.FlatAppearance.BorderSize = 0;
            this.btn_transferToNet.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_transferToNet.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_transferToNet.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_transferToNet.Image = global::RLC.Properties.Resources.down;
            this.btn_transferToNet.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_transferToNet.Location = new System.Drawing.Point(553, 55);
            this.btn_transferToNet.Margin = new System.Windows.Forms.Padding(4);
            this.btn_transferToNet.Name = "btn_transferToNet";
            this.btn_transferToNet.Size = new System.Drawing.Size(164, 49);
            this.btn_transferToNet.TabIndex = 25;
            this.btn_transferToNet.Text = "Transfer to Network";
            this.btn_transferToNet.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_transferToNet.UseVisualStyleBackColor = false;
            this.btn_transferToNet.Click += new System.EventHandler(this.btn_transferToNet_Click);
            this.btn_transferToNet.MouseLeave += new System.EventHandler(this.btn_transferToNet_MouseLeave);
            this.btn_transferToNet.MouseHover += new System.EventHandler(this.btn_transferToNet_MouseHover);
            // 
            // btn_Paste
            // 
            this.btn_Paste.BackColor = System.Drawing.Color.Transparent;
            this.btn_Paste.Enabled = false;
            this.btn_Paste.FlatAppearance.BorderSize = 0;
            this.btn_Paste.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_Paste.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Paste.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Paste.Image = global::RLC.Properties.Resources.paste_icon;
            this.btn_Paste.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_Paste.Location = new System.Drawing.Point(893, 55);
            this.btn_Paste.Margin = new System.Windows.Forms.Padding(4);
            this.btn_Paste.Name = "btn_Paste";
            this.btn_Paste.Size = new System.Drawing.Size(137, 49);
            this.btn_Paste.TabIndex = 24;
            this.btn_Paste.Text = "Paste";
            this.btn_Paste.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_Paste.UseVisualStyleBackColor = false;
            this.btn_Paste.Click += new System.EventHandler(this.pasteProjectToolStripMenuItem_Click);
            this.btn_Paste.MouseLeave += new System.EventHandler(this.btn_Paste_MouseLeave);
            this.btn_Paste.MouseHover += new System.EventHandler(this.btn_Paste_MouseHover);
            // 
            // btn_ConfigUnit
            // 
            this.btn_ConfigUnit.BackColor = System.Drawing.Color.Transparent;
            this.btn_ConfigUnit.Enabled = false;
            this.btn_ConfigUnit.FlatAppearance.BorderSize = 0;
            this.btn_ConfigUnit.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_ConfigUnit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_ConfigUnit.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_ConfigUnit.Image = global::RLC.Properties.Resources.ConfigUnit;
            this.btn_ConfigUnit.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_ConfigUnit.Location = new System.Drawing.Point(265, 55);
            this.btn_ConfigUnit.Margin = new System.Windows.Forms.Padding(4);
            this.btn_ConfigUnit.Name = "btn_ConfigUnit";
            this.btn_ConfigUnit.Size = new System.Drawing.Size(120, 49);
            this.btn_ConfigUnit.TabIndex = 23;
            this.btn_ConfigUnit.Text = "Config I/O";
            this.btn_ConfigUnit.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_ConfigUnit.UseVisualStyleBackColor = false;
            this.btn_ConfigUnit.Click += new System.EventHandler(this.btn_ConfigUnit_Click);
            this.btn_ConfigUnit.MouseLeave += new System.EventHandler(this.btn_ConfigUnit_MouseLeave);
            this.btn_ConfigUnit.MouseHover += new System.EventHandler(this.btn_ConfigUnit_MouseHover);
            // 
            // btn_AddGroup
            // 
            this.btn_AddGroup.BackColor = System.Drawing.Color.Transparent;
            this.btn_AddGroup.FlatAppearance.BorderSize = 0;
            this.btn_AddGroup.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_AddGroup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_AddGroup.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_AddGroup.Image = global::RLC.Properties.Resources.Addgroup;
            this.btn_AddGroup.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_AddGroup.Location = new System.Drawing.Point(385, -1);
            this.btn_AddGroup.Margin = new System.Windows.Forms.Padding(4);
            this.btn_AddGroup.Name = "btn_AddGroup";
            this.btn_AddGroup.Size = new System.Drawing.Size(164, 49);
            this.btn_AddGroup.TabIndex = 22;
            this.btn_AddGroup.Text = "Add Group";
            this.btn_AddGroup.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_AddGroup.UseVisualStyleBackColor = false;
            this.btn_AddGroup.Click += new System.EventHandler(this.btn_AddGroup_Click);
            this.btn_AddGroup.MouseLeave += new System.EventHandler(this.btn_AddGroup_MouseLeave);
            this.btn_AddGroup.MouseHover += new System.EventHandler(this.btn_AddGroup_MouseHover);
            // 
            // btn_EditGroup
            // 
            this.btn_EditGroup.BackColor = System.Drawing.Color.Transparent;
            this.btn_EditGroup.Enabled = false;
            this.btn_EditGroup.FlatAppearance.BorderSize = 0;
            this.btn_EditGroup.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_EditGroup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_EditGroup.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_EditGroup.Image = global::RLC.Properties.Resources.Editgroup;
            this.btn_EditGroup.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_EditGroup.Location = new System.Drawing.Point(568, -1);
            this.btn_EditGroup.Margin = new System.Windows.Forms.Padding(4);
            this.btn_EditGroup.Name = "btn_EditGroup";
            this.btn_EditGroup.Size = new System.Drawing.Size(137, 49);
            this.btn_EditGroup.TabIndex = 19;
            this.btn_EditGroup.Text = "Edit Group";
            this.btn_EditGroup.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_EditGroup.UseVisualStyleBackColor = false;
            this.btn_EditGroup.Click += new System.EventHandler(this.btn_EditGroup_Click);
            this.btn_EditGroup.MouseLeave += new System.EventHandler(this.btn_EditGroup_MouseLeave);
            this.btn_EditGroup.MouseHover += new System.EventHandler(this.btn_EditGroup_MouseHover);
            // 
            // btn_DeleteGroup
            // 
            this.btn_DeleteGroup.BackColor = System.Drawing.Color.Transparent;
            this.btn_DeleteGroup.Enabled = false;
            this.btn_DeleteGroup.FlatAppearance.BorderSize = 0;
            this.btn_DeleteGroup.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_DeleteGroup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_DeleteGroup.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_DeleteGroup.Image = global::RLC.Properties.Resources.Deletegroup;
            this.btn_DeleteGroup.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_DeleteGroup.Location = new System.Drawing.Point(735, -1);
            this.btn_DeleteGroup.Margin = new System.Windows.Forms.Padding(4);
            this.btn_DeleteGroup.Name = "btn_DeleteGroup";
            this.btn_DeleteGroup.Size = new System.Drawing.Size(137, 49);
            this.btn_DeleteGroup.TabIndex = 20;
            this.btn_DeleteGroup.Text = "Delete Group";
            this.btn_DeleteGroup.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_DeleteGroup.UseVisualStyleBackColor = false;
            this.btn_DeleteGroup.Click += new System.EventHandler(this.btn_DeleteGroup_Click);
            this.btn_DeleteGroup.MouseLeave += new System.EventHandler(this.btn_DeleteGroup_MouseLeave);
            this.btn_DeleteGroup.MouseHover += new System.EventHandler(this.btn_DeleteGroup_MouseHover);
            // 
            // btn_AddUnit
            // 
            this.btn_AddUnit.BackColor = System.Drawing.Color.Transparent;
            this.btn_AddUnit.Enabled = false;
            this.btn_AddUnit.FlatAppearance.BorderSize = 0;
            this.btn_AddUnit.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_AddUnit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_AddUnit.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_AddUnit.Image = global::RLC.Properties.Resources.AddUnit;
            this.btn_AddUnit.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_AddUnit.Location = new System.Drawing.Point(1, 55);
            this.btn_AddUnit.Margin = new System.Windows.Forms.Padding(4);
            this.btn_AddUnit.Name = "btn_AddUnit";
            this.btn_AddUnit.Size = new System.Drawing.Size(107, 49);
            this.btn_AddUnit.TabIndex = 18;
            this.btn_AddUnit.Text = "Add Unit";
            this.btn_AddUnit.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_AddUnit.UseVisualStyleBackColor = false;
            this.btn_AddUnit.Click += new System.EventHandler(this.btn_AddUnit_Click);
            this.btn_AddUnit.MouseLeave += new System.EventHandler(this.btn_AddUnit_MouseLeave);
            this.btn_AddUnit.MouseHover += new System.EventHandler(this.btn_AddUnit_MouseHover);
            // 
            // btn_EditUnit
            // 
            this.btn_EditUnit.BackColor = System.Drawing.Color.Transparent;
            this.btn_EditUnit.Enabled = false;
            this.btn_EditUnit.FlatAppearance.BorderSize = 0;
            this.btn_EditUnit.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_EditUnit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_EditUnit.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_EditUnit.Image = global::RLC.Properties.Resources.EditUnit;
            this.btn_EditUnit.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_EditUnit.Location = new System.Drawing.Point(117, 55);
            this.btn_EditUnit.Margin = new System.Windows.Forms.Padding(4);
            this.btn_EditUnit.Name = "btn_EditUnit";
            this.btn_EditUnit.Size = new System.Drawing.Size(141, 49);
            this.btn_EditUnit.TabIndex = 15;
            this.btn_EditUnit.Text = "Edit Unit";
            this.btn_EditUnit.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_EditUnit.UseVisualStyleBackColor = false;
            this.btn_EditUnit.Click += new System.EventHandler(this.btn_EditUnit_Click);
            this.btn_EditUnit.MouseLeave += new System.EventHandler(this.btn_EditUnit_MouseLeave);
            this.btn_EditUnit.MouseHover += new System.EventHandler(this.btn_EditUnit_MouseHover);
            // 
            // btn_DeleteUnit
            // 
            this.btn_DeleteUnit.BackColor = System.Drawing.Color.Transparent;
            this.btn_DeleteUnit.Enabled = false;
            this.btn_DeleteUnit.FlatAppearance.BorderSize = 0;
            this.btn_DeleteUnit.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_DeleteUnit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_DeleteUnit.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_DeleteUnit.Image = global::RLC.Properties.Resources.DeleteUnit;
            this.btn_DeleteUnit.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_DeleteUnit.Location = new System.Drawing.Point(405, 55);
            this.btn_DeleteUnit.Margin = new System.Windows.Forms.Padding(4);
            this.btn_DeleteUnit.Name = "btn_DeleteUnit";
            this.btn_DeleteUnit.Size = new System.Drawing.Size(124, 49);
            this.btn_DeleteUnit.TabIndex = 16;
            this.btn_DeleteUnit.Text = "Delete Unit";
            this.btn_DeleteUnit.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_DeleteUnit.UseVisualStyleBackColor = false;
            this.btn_DeleteUnit.Click += new System.EventHandler(this.btn_DeleteUnit_Click);
            this.btn_DeleteUnit.MouseLeave += new System.EventHandler(this.btn_DeleteUnit_MouseLeave);
            this.btn_DeleteUnit.MouseHover += new System.EventHandler(this.btn_DeleteUnit_MouseHover);
            // 
            // btn_AddProject
            // 
            this.btn_AddProject.BackColor = System.Drawing.Color.Transparent;
            this.btn_AddProject.FlatAppearance.BorderSize = 0;
            this.btn_AddProject.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_AddProject.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_AddProject.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_AddProject.Image = global::RLC.Properties.Resources.add;
            this.btn_AddProject.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_AddProject.Location = new System.Drawing.Point(1, -1);
            this.btn_AddProject.Margin = new System.Windows.Forms.Padding(4);
            this.btn_AddProject.Name = "btn_AddProject";
            this.btn_AddProject.Size = new System.Drawing.Size(107, 49);
            this.btn_AddProject.TabIndex = 14;
            this.btn_AddProject.Text = "&Add Project";
            this.btn_AddProject.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_AddProject.UseVisualStyleBackColor = false;
            this.btn_AddProject.Click += new System.EventHandler(this.btn_AddProject_Click);
            this.btn_AddProject.MouseLeave += new System.EventHandler(this.btn_AddProject_MouseLeave);
            this.btn_AddProject.MouseHover += new System.EventHandler(this.btn_AddProject_MouseHover);
            // 
            // btn_RenameProject
            // 
            this.btn_RenameProject.BackColor = System.Drawing.Color.Transparent;
            this.btn_RenameProject.Enabled = false;
            this.btn_RenameProject.FlatAppearance.BorderSize = 0;
            this.btn_RenameProject.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_RenameProject.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_RenameProject.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_RenameProject.Image = global::RLC.Properties.Resources.renamer_5329_011;
            this.btn_RenameProject.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_RenameProject.Location = new System.Drawing.Point(117, -1);
            this.btn_RenameProject.Margin = new System.Windows.Forms.Padding(4);
            this.btn_RenameProject.Name = "btn_RenameProject";
            this.btn_RenameProject.Size = new System.Drawing.Size(141, 49);
            this.btn_RenameProject.TabIndex = 0;
            this.btn_RenameProject.Text = "&Rename Project";
            this.btn_RenameProject.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_RenameProject.UseVisualStyleBackColor = false;
            this.btn_RenameProject.Click += new System.EventHandler(this.btn_RenameProject_Click);
            this.btn_RenameProject.MouseLeave += new System.EventHandler(this.btn_RenameProject_MouseLeave);
            this.btn_RenameProject.MouseHover += new System.EventHandler(this.btn_RenameProject_MouseHover);
            // 
            // btn_CopyProject
            // 
            this.btn_CopyProject.BackColor = System.Drawing.Color.Transparent;
            this.btn_CopyProject.Enabled = false;
            this.btn_CopyProject.FlatAppearance.BorderSize = 0;
            this.btn_CopyProject.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_CopyProject.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_CopyProject.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_CopyProject.Image = global::RLC.Properties.Resources.copy1;
            this.btn_CopyProject.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_CopyProject.Location = new System.Drawing.Point(735, 55);
            this.btn_CopyProject.Margin = new System.Windows.Forms.Padding(4);
            this.btn_CopyProject.Name = "btn_CopyProject";
            this.btn_CopyProject.Size = new System.Drawing.Size(137, 49);
            this.btn_CopyProject.TabIndex = 2;
            this.btn_CopyProject.Text = "Copy";
            this.btn_CopyProject.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_CopyProject.UseVisualStyleBackColor = false;
            this.btn_CopyProject.Click += new System.EventHandler(this.btn_CopyProject_Click);
            this.btn_CopyProject.MouseLeave += new System.EventHandler(this.btn_CopyProject_MouseLeave);
            this.btn_CopyProject.MouseHover += new System.EventHandler(this.btn_CopyProject_MouseHover);
            // 
            // btn_DeleteProject
            // 
            this.btn_DeleteProject.BackColor = System.Drawing.Color.Transparent;
            this.btn_DeleteProject.Enabled = false;
            this.btn_DeleteProject.FlatAppearance.BorderSize = 0;
            this.btn_DeleteProject.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Transparent;
            this.btn_DeleteProject.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_DeleteProject.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_DeleteProject.Image = global::RLC.Properties.Resources.delete;
            this.btn_DeleteProject.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_DeleteProject.Location = new System.Drawing.Point(265, -1);
            this.btn_DeleteProject.Margin = new System.Windows.Forms.Padding(4);
            this.btn_DeleteProject.Name = "btn_DeleteProject";
            this.btn_DeleteProject.Size = new System.Drawing.Size(124, 49);
            this.btn_DeleteProject.TabIndex = 1;
            this.btn_DeleteProject.Text = "&Delete Project";
            this.btn_DeleteProject.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.btn_DeleteProject.UseVisualStyleBackColor = false;
            this.btn_DeleteProject.Click += new System.EventHandler(this.btn_DeleteProject_Click);
            this.btn_DeleteProject.MouseLeave += new System.EventHandler(this.btn_DeleteProject_MouseLeave);
            this.btn_DeleteProject.MouseHover += new System.EventHandler(this.btn_DeleteProject_MouseHover);
            // 
            // panel_firmware
            // 
            this.panel_firmware.Controls.Add(this.lbip);
            this.panel_firmware.Controls.Add(this.label3);
            this.panel_firmware.Controls.Add(this.label4);
            this.panel_firmware.Controls.Add(this.lb_percent);
            this.panel_firmware.Controls.Add(this.prBar_Update);
            this.panel_firmware.Location = new System.Drawing.Point(259, 364);
            this.panel_firmware.Margin = new System.Windows.Forms.Padding(4);
            this.panel_firmware.Name = "panel_firmware";
            this.panel_firmware.Size = new System.Drawing.Size(1085, 52);
            this.panel_firmware.TabIndex = 39;
            this.panel_firmware.Visible = false;
            this.panel_firmware.Paint += new System.Windows.Forms.PaintEventHandler(this.panel_firmware_Paint);
            // 
            // lbip
            // 
            this.lbip.AutoSize = true;
            this.lbip.Location = new System.Drawing.Point(128, 12);
            this.lbip.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lbip.Name = "lbip";
            this.lbip.Size = new System.Drawing.Size(24, 17);
            this.lbip.TabIndex = 40;
            this.lbip.Text = "IP:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(357, 10);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(69, 17);
            this.label3.TabIndex = 37;
            this.label3.Text = "Progress:";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(408, 33);
            this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(301, 17);
            this.label4.TabIndex = 36;
            this.label4.Text = "Loading firmware. Please wait a few seconds...";
            // 
            // lb_percent
            // 
            this.lb_percent.AutoSize = true;
            this.lb_percent.Location = new System.Drawing.Point(717, 11);
            this.lb_percent.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lb_percent.Name = "lb_percent";
            this.lb_percent.Size = new System.Drawing.Size(28, 17);
            this.lb_percent.TabIndex = 35;
            this.lb_percent.Text = "0%";
            // 
            // prBar_Update
            // 
            this.prBar_Update.Location = new System.Drawing.Point(436, 7);
            this.prBar_Update.Margin = new System.Windows.Forms.Padding(4);
            this.prBar_Update.Name = "prBar_Update";
            this.prBar_Update.Size = new System.Drawing.Size(260, 21);
            this.prBar_Update.TabIndex = 34;
            // 
            // RLC1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1341, 854);
            this.Controls.Add(this.panel_firmware);
            this.Controls.Add(this.panel3);
            this.Controls.Add(this.panel_Project);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.menuStrip1);
            this.Controls.Add(this.treeView1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MainMenuStrip = this.menuStrip1;
            this.Margin = new System.Windows.Forms.Padding(4);
            this.MaximumSize = new System.Drawing.Size(1359, 6143);
            this.Name = "RLC1";
            this.Text = "RLC Toolkit Version 3.0.5-Admin";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.RLC1_FormClosing);
            this.Load += new System.EventHandler(this.RLC1_Load);
            this.panel1.ResumeLayout(false);
            this.panel3.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel4.ResumeLayout(false);
            this.panel4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grBoardNetwork)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gr_Unit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gr_Group)).EndInit();
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.contextMenuStrip_Project.ResumeLayout(false);
            this.contextMenuStrip_Root.ResumeLayout(false);
            this.contextMenuStrip1.ResumeLayout(false);
            this.panel_Project.ResumeLayout(false);
            this.panel_firmware.ResumeLayout(false);
            this.panel_firmware.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.styleController1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        public System.Windows.Forms.TreeView treeView1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Label label1;
        public System.Windows.Forms.DataGridView gr_Group;
        private System.Windows.Forms.MenuStrip menuStrip1;
        private System.Windows.Forms.ToolStripMenuItem fileToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem editToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem projectToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem hellpToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem exitToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem fileToolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem editToolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem projectToolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem toolToolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem helpToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem renameProjectAltRToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem copyProjectToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem addProjectAltAToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem deleteProjectToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem pasteProjectToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripRestoreProject;
        private System.Windows.Forms.ToolStripMenuItem toolStripBackup;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem exitToolStripMenuItem1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip_Project;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem5;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem6;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem7;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem8;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem4;
        private System.Windows.Forms.ToolStripMenuItem aboutToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem updateFirmwareToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem addProJectToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip_Root;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem tstripChild_copy;
        private System.Windows.Forms.ToolStripMenuItem tstripChild_Paste;
        public System.Windows.Forms.DataGridView gr_Unit;
        private System.Windows.Forms.Button btn_DeleteProject;
        private System.Windows.Forms.Button btn_CopyProject;
        private System.Windows.Forms.Button btn_RenameProject;
        private System.Windows.Forms.Button btn_AddProject;
        public System.Windows.Forms.Button btn_DeleteUnit;
        public System.Windows.Forms.Button btn_EditUnit;
        private System.Windows.Forms.Button btn_AddUnit;
        public System.Windows.Forms.Button btn_DeleteGroup;
        public System.Windows.Forms.Button btn_EditGroup;
        private System.Windows.Forms.Button btn_AddGroup;
        public System.Windows.Forms.Button btn_ConfigUnit;
        private System.Windows.Forms.Button btn_Paste;
        private System.Windows.Forms.Button btn_transferToNet;
        private System.Windows.Forms.Panel panel_Project;
        public System.Windows.Forms.DataGridView grBoardNetwork;
        private System.Windows.Forms.Panel panel4;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Button btn_TranfertoData;
        public System.Windows.Forms.Button btn_AddtoData;
        private System.Windows.Forms.Button btn_Scan;
        public System.Windows.Forms.Button btn_ConfigUnitNetwork;
        public System.Windows.Forms.Button btn_ReplaceDatabase;
        private System.Windows.Forms.Button btn_UpdateFirmware;
        public System.Windows.Forms.Button btn_EditUnitNetwork;
        private System.Windows.Forms.Panel panel_firmware;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label lb_percent;
        private System.Windows.Forms.ProgressBar prBar_Update;
        private DevExpress.XtraEditors.StyleController styleController1;
        private System.Windows.Forms.ToolStripMenuItem setupToolStripMenuItem;
        private System.Windows.Forms.DataGridViewTextBoxColumn GroupName;
        private System.Windows.Forms.DataGridViewTextBoxColumn Address;
        private System.Windows.Forms.DataGridViewTextBoxColumn Descripts;
        private System.Windows.Forms.Label lbip;
        private System.Windows.Forms.DataGridViewCheckBoxColumn SelectUnit;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
        private System.Windows.Forms.DataGridViewCheckBoxColumn dataGridViewCheckBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn Fw_Ver;
        private System.Windows.Forms.DataGridViewTextBoxColumn HW_Ver;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
        private System.Windows.Forms.DataGridViewTextBoxColumn Man_Date;
        private System.Windows.Forms.DataGridViewCheckBoxColumn Network_Chk_Recovery;
        private System.Windows.Forms.DataGridViewTextBoxColumn Unit_Type;
        private System.Windows.Forms.DataGridViewTextBoxColumn Barcode;
        private System.Windows.Forms.DataGridViewTextBoxColumn gr_IPAddress;
        private System.Windows.Forms.DataGridViewTextBoxColumn ID_Can;
        private System.Windows.Forms.DataGridViewTextBoxColumn ActMode;
        private System.Windows.Forms.DataGridViewCheckBoxColumn LoadCan;
        private System.Windows.Forms.DataGridViewTextBoxColumn db_fw_ver;
        private System.Windows.Forms.DataGridViewTextBoxColumn db_Hw_Ver;
        private System.Windows.Forms.DataGridViewTextBoxColumn Descript;
        private System.Windows.Forms.DataGridViewCheckBoxColumn DataBase_Chk_Recovery;
    }
}

