<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btn_Edit_Group.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAktJREFUOE+N
        kF1IU2EYx5cSeGFuYMmWuZ3aIL1YmYhtRGFJEOWFa3oziEajwNVk4XWcu0AqiC5iwejsq20elpvNfaSZ
        qInaphFZaUKWWdvONvfhDM3q6T3x3rW5fvDnvO/z/J5z3vNyiuEmiDanQPB+gCDu+giCj8v/T69Cces+
        l7tFVVb+8NTUjI/W1kpxqzjx6Z7WmckguPV6MAiF8IDHAyefn/BWV7dgpTDrs+Te3MTV18zgGVgJ3YS5
        5y/gYVMTGCoqfqLTqLGWH6A7SnOTXXeSQ2ch2n8QVpy74HNQAR/ejEOv5kroNoezG6v5yU10X0gNKzdj
        Aw0Q8Yjh62M+xLyHYdklXpudDsixlp+NsE6cHlV9jPmOAOOX/x1m/Efhm/vA5jJd3om1wmTGLrqYgfpf
        iWAzRL1S9OVDwPgaIdInGGRoTjnW/gWAs2NtVHMtHjgBTEAOqWEFMOgX2HuIPamLRmnufqzmJzVC8pJT
        PTOxoXOQGVND4ulJ9JI2SPiPfY/0SzqwVpjk1L19yVeGxfRbO6RDN2B1pANWn7VC3FvvXKKIMqwVJrtA
        1WbeWSPpBRdkPgUhu+SB5JhqMeKX7MHK9hyX1TVc17Zn51/afue+jCTWY+G+jcycBLeLIxKJTlVVVW3J
        GqWPlufRtYfDO3FreyiKKjObzUK9Xt+p1Wq77Xa7Eu3bUVQ2m+0SG3bN1iwWy3mr1Xoa1WRGo1FEkmQJ
        h6bpUlSUOByOZpPJpECSGkWDJB16drFBwzqUy2wPOUqUFnaGJMmSP0PmOvO78K7cAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btn_Add_Group.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAndJREFUOE+l
        kl1IU2EYx0dEUdFNeZmpOQ2HWlKYZGZBow/DG+uqZOVNQoEfcyJqVJuLzEoULSPWTJeBRrWaa8v0HKfz
        sM1tztbmXFM8QoWK5TEovfn3nsMhMAKFfvDAy/P7P4fzvjySf8EhuH1uaahukntdMc1Z7s4tOxSiWjuD
        Ee1i54j8R6dHvmgL1ywCWC+q1VkAm97hOY5G92Y0uDahffgYZjGRL+rVWcCMVO/KRq1dSioBOmcWOEzl
        inptPHYdgnZgt1A6ZybCCG8U1Uo4hKI+sOZ4espAmT7VUubxOsoyUUI1M5m4MRiD6wMxaBo6CEukhLg7
        JHOL6mefUP4Zi1T4wDLGCpzTD2Y11hSobdHQ0rugeU/OvTKo6Tio++Og6ZORXqrg1LYd0FhlGGLr55cQ
        zCP39W8zfmxkqq0HoCID5WSwjEpEZa8U13riharskwo93qnoWFT3pOPlaJ3jK3xbJL8QSbSGG3wVxjQo
        3+6EyixFqTkFpaYkqEyJKDMlkLMMJd3JxCWQTDQq3+yFJXQv8hOTsRLAv8Hz+dn+VvtVfQt9Wd9qKzYY
        AzUzZc+PotCYjEsvZFB2HcGroOaLji5qa7GRjL1KPzb/Ln8WY1uFd/ibbxipVz7NgaIzFec7klHUfgLf
        MVov6rVx0ZCBM4/2IO9hCs7p95FFxDpRrQ4HLuqsLgOHm5KQ3SxD7v00zJHtFPVK+B0PhUInnU7nBVLF
        bre7K+Dy0Kq2K8ipkeO0Vg6lvhCk1887PsNnA4HAqT9/xbJsltfrVTgcDgXDMIrJ4eBNJuSosnq7VRaP
        WWkfZ8onXIHbvOMzPp9Pwc8Iw/+HRPIbv+KTMEUz4+IAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="dataGridViewTextBoxColumn1.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="dataGridViewTextBoxColumn2.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Group.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Preset.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>648, 56</value>
  </metadata>
</root>